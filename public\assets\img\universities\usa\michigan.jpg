 
<!DOCTYPE html>
<!--[if (gt IE 9)|!(IE)]> <!--> <html lang="en" class="no-js "  itemscope xmlns:og="http://opengraphprotocol.org/schema/"> <!--<![endif]-->
<!--[if IE 9]> <html lang="en" class="no-js ie9 lt-ie10 " xmlns:og="http://opengraphprotocol.org/schema/"> <![endif]-->
<!--[if IE 8]> <html lang="en" class="no-js ie8 lt-ie10 lt-ie9 " xmlns:og="http://opengraphprotocol.org/schema/"> <![endif]-->
<!--[if (lt IE 8)]> <html lang="en" class="no-js lt-ie10 lt-ie9 lt-ie8 " xmlns:og="http://opengraphprotocol.org/schema/"> <![endif]-->
<head>
    <title>Page Not Found</title>
        <meta name="errorpage" content="true" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="sourceApp" content="nyt-v5" />
    <meta id="foundation-build-id" name="foundation-build-id" content="" />
    <meta name="errortype" content="404 - Not Found" />
    <meta name="PST" content="" />

            <!--[if (gt IE 9)|!(IE)]> <!-->
    <link rel="stylesheet" type="text/css" media="screen" href="https://g1.nyt.com/assets/error/20180503-144802/css/error/styles.css" />
<!--<![endif]-->
<!--[if lte IE 9]>
    <link rel="stylesheet" type="text/css" media="screen" href="https://g1.nyt.com/assets/error/20180503-144802/css/error/styles-ie.css" />
<![endif]-->
            <!--  begin abra  -->
<!--esi
<esi:include src="http://wp-aballoc-service/v9/document-setup.html-esi?apps=www-error+ecomm-fe&amp;etUrl=prd" />
-->
<!--  end abra  -->

<!--esi
<script id="user-info-data" type="application/json">
<esi:include src="/svc/web-products/userinfo-v3.json" />
</script>
-->
<script id="magnum-feature-flags" type="application/json">["removeInternationalEdition","caslOpt","jkiddScript"]</script>
<script>
var require = {
    baseUrl: 'https://g1.nyt.com/assets/',
    waitSeconds: 20,
    paths: {
        'foundation': 'error/20180503-144802/js/foundation',
        'shared': 'error/20180503-144802/js/shared',
        'error': 'error/20180503-144802/js/error',
        'application': 'error/20180503-144802/js/error/',
        'videoFactory': 'https://static01.nyt.com/js2/build/video/2.0/videofactoryrequire',
        'videoPlaylist': 'https://static01.nyt.com/js2/build/video/players/extended/2.0/appRequire',
        'auth/mtr': 'https://static01.nyt.com/js/mtr',
        'auth/growl': 'https://static01.nyt.com/js/auth/growl/default',
        'vhs': 'https://static01.nyt.com/video/vhs/build/vhs-2.x.min',
        'vhs3': 'https://static01.nyt.com/video-static/vhs3/vhs.min'
    }
};
</script>
<!--[if (gte IE 9)|!(IE)]> <!-->
<script data-main="foundation/main" src="https://g1.nyt.com/assets/error/20180503-144802/js/foundation/lib/framework.js"></script>
<!--<![endif]-->
<!--[if lt IE 9]>
<script>
    require.map = { '*': { 'foundation/main': 'foundation/legacy_main' } };
</script>
<script data-main="foundation/legacy_main" src="https://g1.nyt.com/assets/error/20180503-144802/js/foundation/lib/framework.js"></script>
<![endif]-->
</head>
<body>

    <style>
    .lt-ie10 .messenger.suggestions {
        display: block !important;
        height: 50px;
    }

    .lt-ie10 .messenger.suggestions .message-bed {
        background-color: #f8e9d2;
        border-bottom: 1px solid #ccc;
    }

    .lt-ie10 .messenger.suggestions .message-container {
        padding: 11px 18px 11px 30px;
    }

    .lt-ie10 .messenger.suggestions .action-link {
        font-family: "nyt-franklin", arial, helvetica, sans-serif;
        font-size: 10px;
        font-weight: bold;
        color: #a81817;
        text-transform: uppercase;
    }

    .lt-ie10 .messenger.suggestions .alert-icon {
        background: url('https://static01.nyt.com/images/icons/icon-alert-12x12-a81817.png') no-repeat;
        width: 12px;
        height: 12px;
        display: inline-block;
        margin-top: -2px;
        float: none;
    }

    .lt-ie10 .masthead,
    .lt-ie10 .navigation,
    .lt-ie10 .comments-panel {
        margin-top: 50px !important;
    }

    .lt-ie10 .ribbon {
        margin-top: 97px !important;
    }
</style>
<div id="suggestions" class="suggestions messenger nocontent robots-nocontent" style="display:none;">
    <div class="message-bed">
        <div class="message-container last-message-container">
            <div class="message">
                <span class="message-content">
                    <i class="icon alert-icon"></i><span class="message-title">NYTimes.com no longer supports Internet Explorer 9 or earlier. Please upgrade your browser.</span>
                    <a href="http://www.nytimes.com/content/help/site/ie9-support.html" class="action-link">LEARN MORE »</a>
                </span>
            </div>
        </div>
    </div>
</div>

    <div id="shell" class="shell">
    <header id="masthead" class="masthead masthead-theme-standard" role="banner">
    <div class="container">
        <div class="quick-navigation button-group">
            <button class="button sections-button"><i class="icon sprite-icon"></i><span class="button-text">Sections</span></button>
            <button class="button home-button" data-href="https://www.nytimes.com/" title="Go to the home page to see the latest top stories."><i class="icon sprite-icon"></i>
                <span class="button-text">Home</span>
            </button>
            <button class="button search-button"><i class="icon sprite-icon"></i><span class="button-text">Search</span></button>
            <a class="button skip-button skip-to-content visually-hidden focusable" href="#main">Skip to content</a>
                                </div><!-- close button-group -->
        <div class="branding">
            <h2 class="branding-heading">
                <a id="branding-heading-link" href="https://www.nytimes.com/">
                    <span class="visually-hidden">The New York Times</span>
                </a>
            </h2>
            <script>window.magnum.writeLogo('small', 'https://g1.nyt.com/assets/error/20180503-144802/images/foundation/logos/', '', 'masthead-theme-standard', '', 'branding-heading-link', 'error');</script>
        </div><!-- close branding -->
                <div class="user-tools">
            <div id="Bar1" class="ad bar1-ad nocontent robots-nocontent"></div>
<div id="liftoff-nyt5-bar1" class="hidden"></div>
                        <button class="button search-button"><i class="icon sprite-icon"></i><span class="button-text">Search</span></button>
            <div class="user-tools-button-group button-group">
                <button class="button subscribe-button hidden" data-href="https://www.nytimes.com/subscriptions/Multiproduct/lp3004.html?campaignId=4XUYF">Subscribe Now</button>
                <button class="button login-button login-modal-trigger hidden">Log In</button>
                                                <button class="button notifications-button hidden"><i class="icon sprite-icon"></i><span class="button-text">0</span></button>
                <button class="button user-settings-button"><i class="icon sprite-icon"></i><span class="button-text">Settings</span></button>
            </div><!-- close user-tools-button-group -->
        </div><!-- close user-tools -->
    </div><!-- close container -->
    <div class="search-flyout-panel flyout-panel">
    <button class="button close-button" type="button"><i class="icon"></i><span class="visually-hidden">Close search</span></button>
    <nav class="search-form-control form-control layout-horizontal">
    <h2 class="visually-hidden">Site Search Navigation</h2>
    <form class="search-form" role="search">
        <div class="control">
            <div class="label-container visually-hidden">
                                <label for="search-input-2">Search NYTimes.com</label>
                            </div>
            <div class="field-container">
                                <input id="search-input-2" name="search-input-2" type="text" class="search-input text" autocomplete="off" placeholder="Search NYTimes.com" />

                <button type="button" class="button clear-button" tabindex="-1" aria-describedby="clear-search-input"><i class="icon"></i><span id="clear-search-input" class="visually-hidden">Clear this text input</span></button>
                <div class="auto-suggest" style="display: none;">
                    <ol></ol>
                </div>
                <button class="button submit-button" type="submit">Go</button>
            </div>
        </div><!-- close control -->
    </form>
</nav>


</div><!-- close flyout-panel -->
    <div id="notification-modals" class="notification-modals"></div>
</header>
        <nav id="navigation" class="navigation">
    <h2 class="visually-hidden">Site Navigation</h2>
</nav><!-- close navigation -->

<nav id="mobile-navigation" class="mobile-navigation hidden">
    <h2 class="visually-hidden">Site Mobile Navigation</h2>
</nav><!-- close mobile-navigation -->

    <div id="navigation-edge" class="navigation-edge"></div>
    <div id="page" class="page">
        <main id="main" class="main" role="main">
                <article class="error-page">
    <header class="error-header">
        <h1>Page Not Found</h1>
        <h2>We&#39re sorry, we seem to have lost this page,<br /> but we don&#39t want to lose you.</h2>
    </header>

    <nav class="search-form-control form-control layout-horizontal">
    <h2 class="visually-hidden">Site Search Navigation</h2>
    <form class="search-form" role="search">
        <div class="control">
            <div class="label-container visually-hidden">
                                <label for="search-input-2">Search NYTimes.com</label>
                            </div>
            <div class="field-container">
                                <input id="search-input-2" name="search-input-2" type="text" class="search-input text" autocomplete="off" placeholder="Search NYTimes.com" />

                <button type="button" class="button clear-button" tabindex="-1" aria-describedby="clear-search-input"><i class="icon"></i><span id="clear-search-input" class="visually-hidden">Clear this text input</span></button>
                <div class="auto-suggest" style="display: none;">
                    <ol></ol>
                </div>
                <button class="button submit-button" type="submit">Go</button>
            </div>
        </div><!-- close control -->
    </form>
</nav>



    <ul class="menu layout-horizontal theme-links with-pipes">
        <li>
            <a href="/">Go to Home Page</a>
        </li>
    </ul>

    <section id="whats-next" class="whats-next nocontent robots-nocontent">
    <h2 class="visually-hidden">What's Next</h2>
    <div class="nocontent robots-nocontent">
        <div class="loader-container">
            <div class="loader loader-t-logo-32x32-ecedeb-ffffff"><span class="visually-hidden">Loading...</span></div>
        </div>
    </div><!-- close nocontent -->
</section>

</article>
                    <div class="search-overlay"></div>
            </main><!-- close main -->
            <section id="site-index" class="site-index">
    <header class="section-header">
        <p class="user-action"><a href="https://www.nytimes.com/">Go to Home Page &raquo;</a></p>
        <h2 class="section-heading">
            <span class="visually-hidden">Site Index</span>
            <a id="site-index-branding-link" href="https://www.nytimes.com/">
                <span class="visually-hidden">The New York Times</span>
            </a>
        </h2>
        <script>window.magnum.writeLogo('small', 'https://g1.nyt.com/assets/error/20180503-144802/images/foundation/logos/', '', '', '', 'site-index-branding-link', '');</script>
    </header>

    <nav id="site-index-navigation" class="site-index-navigation" role="navigation">
        <h2 class="visually-hidden">Site Index Navigation</h2>
        <div class="split-6-layout layout">


                    <div class="column">
                        <h3 class="menu-heading">News</h3>
                        <ul class="menu">


                                    <li>
                                        <a href="https://www.nytimes.com/section/world">World</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/us">U.S.</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/politics">Politics</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/nyregion">N.Y.</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/business">Business</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/technology">Tech</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/science">Science</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/health">Health</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/sports">Sports</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/education">Education</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/obituaries">Obituaries</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/pages/todayspaper/index.html">Today's Paper</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/corrections">Corrections</a>
                                    </li>


                        </ul>
                    </div><!-- close column -->


                    <div class="column">
                        <h3 class="menu-heading">Opinion</h3>
                        <ul class="menu">


                                    <li>
                                        <a href="https://www.nytimes.com/pages/opinion/index.html">Today's Opinion</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/opinion/columnists">Op-Ed Columnists</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/opinion/editorials">Editorials</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/opinion/contributors">Op-Ed Contributors</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/opinion/letters">Letters</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/opinion/sunday">Sunday Review</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/video/opinion">Video: Opinion</a>
                                    </li>


                        </ul>
                    </div><!-- close column -->


                    <div class="column">
                        <h3 class="menu-heading">Arts</h3>
                        <ul class="menu">


                                    <li>
                                        <a href="https://www.nytimes.com/section/arts">Today's Arts</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/arts/design">Art & Design</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/books/review">Book Review</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/arts/dance">Dance</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/movies">Movies</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/arts/music">Music</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/events/">N.Y.C. Events Guide</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/arts/television">Television</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/theater">Theater</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/video/arts">Video: Arts</a>
                                    </li>


                        </ul>
                    </div><!-- close column -->


                    <div class="column">
                        <h3 class="menu-heading">Living</h3>
                        <ul class="menu">


                                    <li>
                                        <a href="https://www.nytimes.com/section/automobiles">Automobiles</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/crosswords">Crossword</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/pages/dining/index.html">Food</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/education">Education</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/fashion">Fashion & Style</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/health">Health</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/jobs">Jobs</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/magazine">Magazine</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/events/">N.Y.C. Events Guide</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/realestate">Real Estate</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/t-magazine">T Magazine</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/travel">Travel</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/fashion/weddings">Weddings & Celebrations</a>
                                    </li>


                        </ul>
                    </div><!-- close column -->


                    <div class="column">
                        <h3 class="menu-heading">Listings & More</h3>
                        <ul class="menu">


                                    <li>
                                        <a href="https://www.nytimes.com/section/reader-center">Reader Center</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/ref/classifieds/">Classifieds</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/marketing/tools-and-services/">Tools & Services</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/events/">N.Y.C. Events Guide</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/section/multimedia">Multimedia</a>
                                    </li>


                                    <li>
                                        <a href="https://lens.blogs.nytimes.com/">Photography</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/video">Video</a>
                                    </li>


                                    <li>
                                        <a href="https://store.nytimes.com/?action=click&contentCollection=NYT%20Store&contentPlacement=2&module=SectionsNav&pgtype=Homepage&region=TopBar&t=qry542&utm_campaign=NYT-HP&utm_content=hp_browsetree&utm_medium=HPB&utm_source=nytimes&version=BrowseTree">NYT Store</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/times-journeys/?utm_source=nytimes&utm_medium=HPLink&utm_content=hp_browsetree&utm_campaign=NYT-HP">Times Journeys</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/seeallnav">Subscribe</a>
                                    </li>


                                    <li>
                                        <a href="https://www.nytimes.com/membercenter">Manage My Account</a>
                                    </li>


                                    <li>
                                        <a href="http://www.nytco.com">NYTCo</a>
                                    </li>


                        </ul>
                    </div><!-- close column -->


            <div class="column last-column">

<h3 class="menu-heading">Subscribe</h3>

<ul class="menu primary-menu">
    <li class="menu-label">Subscribe</li>
    <li class="home-delivery">
        <i class="icon sprite-icon"></i>
                    <a class="nyt-home-delivery" href="https://www.nytimes.com/hdleftnav">Home Delivery</a>
            </li>
    <li class="digital-subscriptions">
        <i class="icon sprite-icon"></i>
                    <a class="digital-subscription" href="https://www.nytimes.com/digitalleftnav">Digital Subscriptions</a>
            </li>
    <li class="nyt-crossword last-item">
        <i class="icon sprite-icon"></i>
        <a id="nyt-crossword" href="https://www.nytimes.com/crosswords">Crossword</a>
    </li>
</ul>

<ul class="menu secondary-menu">

    <li class="email-newsletters">
        <a href="https://www.nytimes.com/marketing/newsletters">Email Newsletters</a>
    </li>
    <li>
        <a href="https://myaccount.nytimes.com/mem/tnt.html">Alerts</a>
    </li>
    <li class="gift-subscription">
                    <a href="https://www.nytimes.com/giftleftnav">Gift Subscriptions</a>
            </li>
    <li>
                    <a href="https://www.nytimes.com/corporateleftnav">Group Subscriptions</a>
            </li>
    <li>
                    <a href="https://www.nytimes.com/educationleftnav">Education Rate</a>
            </li>

</ul>
<ul class="menu secondary-menu">
    <li>
        <a href="https://www.nytimes.com/services/mobile/index.html">Mobile Applications</a>
    </li>
    <li>
                    <a href="http://eedition.nytimes.com/cgi-bin/signup.cgi?cc=37FYY">Replica Edition</a>
            </li>

</ul>
            </div><!-- close column -->

        </div><!-- close split-6-layout -->

    </nav><!-- close nav -->

</section><!-- close site-index -->

            <footer id="page-footer" class="page-footer" role="contentinfo">
    <nav>
        <h2 class="visually-hidden">Site Information Navigation</h2>
         <ul>
             <li>
                <a href="https://help.nytimes.com/hc/en-us/articles/115014792127-Copyright-notice" itemprop="copyrightNotice">
                    &copy; <span itemprop="copyrightYear">2018</span><span itemprop="copyrightHolder provider sourceOrganization" itemscope itemtype="http://schema.org/Organization" itemid="http://www.nytimes.com"><span itemprop="name"> The New York Times Company</span><meta itemprop="tickerSymbol" content="NYSE NYT"/></span>
                </a>
            </li>
            <li class="visually-hidden"><a href="https://www.nytimes.com">Home</a></li>
            <li class="visually-hidden"><a href="http://query.nytimes.com/search/sitesearch/#/">Search</a></li>
            <li class="visually-hidden">Accessibility concerns? Email us at <a href="mailto:<EMAIL>"><EMAIL></a>. We would love to hear from you.</li>
            <li class="wide-viewport-item"><a href="https://www.nytimes.com/ref/membercenter/help/infoservdirectory.html">Contact Us</a></li>
            <li class="wide-viewport-item"><a href="http://www.nytco.com/careers">Work With Us</a></li>
            <li class="wide-viewport-item"><a href="http://nytmediakit.com/">Advertise</a></li>
            <li class="wide-viewport-item"><a href="https://www.nytimes.com/content/help/rights/privacy/policy/privacy-policy.html#pp">Your Ad Choices</a></li>
            <li><a href="https://www.nytimes.com/privacy">Privacy</a></li>
            <li><a href="https://help.nytimes.com/hc/en-us/articles/115014893428-Terms-of-service" itemprop="usageTerms">Terms of Service</a></li>
            <li class="wide-viewport-item last-item"><a href="https://help.nytimes.com/hc/en-us/articles/115014893968-Terms-of-sale">Terms of Sale</a></li>
         </ul>
    </nav>
    <nav class="last-nav">
        <h2 class="visually-hidden">Site Information Navigation</h2>
        <ul>
            <li><a href="http://spiderbites.nytimes.com">Site Map</a></li>
            <li><a href="https://help.nytimes.com/hc/en-us">Help</a></li>
            <li><a href="https://help.nytimes.com/hc/en-us/articles/115015385887-Contact-us">Site Feedback</a></li>
            <li class="wide-viewport-item last-item"><a href="https://www.nytimes.com/subscriptions/Multiproduct/lp5558.html?campaignId=37WXW">Subscriptions</a></li>
        </ul>
    </nav>
</footer>
        </div><!-- close page -->
    </div><!-- close shell -->
    <script>
require(['foundation/main'], function () {
    require(['error/main']);




    require(['jquery/nyt', 'foundation/views/page-manager'], function ($, pageManager) {
        if (window.location.search.indexOf('disable_tagx') > 0) {
            return;
        }
        $(document).ready(function () {
            require(['https://a1.nyt.com/analytics/json-kidd.min.js'], function () {
                pageManager.trackingFireEventQueue();
            });
        });
    });
});
</script>

    <div id="Inv1" class="ad inv1-ad hidden"></div>
<div id="Inv2" class="ad inv2-ad hidden"></div>
<div id="Inv3" class="ad inv3-ad hidden"></div>
<div id="ab1" class="ad ab1-ad hidden"></div>
<div id="ab2" class="ad ab2-ad hidden"></div>
<div id="ab3" class="ad ab3-ad hidden"></div>
<div id="prop1" class="ad prop1-ad hidden"></div>
<div id="prop2" class="ad prop2-ad hidden"></div>
<div id="Anchor" class="ad anchor-ad hidden"></div>
<div id="ADX_CLIENTSIDE" class="ad adx-clientside-ad hidden"></div>
</body>
</html>
 